"""
Base Django settings for config project.
"""

import os

from pathlib import Path
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.celery import CeleryIntegration
from sentry_sdk.integrations.redis import RedisIntegration
from firebase_admin import initialize_app
from corsheaders.defaults import default_headers
from dxh_libraries.translation import gettext_lazy as _

from config.env import env
from config.extend.recaptcha_setting import *  # noqa F403
from config.extend.jwt_setting import SIMPLE_JWT
from config.extend.unfold_setting import UNFOLD_CONFIG  # noqa F403
from config.extend.richeditor_setting import QUILL_CONFIG
from config.extend.fcm_django_settings import FCM_DJANGO_SETTINGS
# from config.extend.logging_setting import LOGGING
from config.extend.email_setting import *



# === Paths === #
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# === Load Environment Variables === #


ADMIN_URL = env("DJANGO_ADMIN_URL", default="admin/")

# === Security === #
SECRET_KEY = env("DJANGO_SECRET_KEY", default='django-insecure-#&')
DEBUG = env.bool("DJANGO_DEBUG", default=False)
ALLOWED_HOSTS = env.list("DJANGO_ALLOWED_HOSTS", default="localhost,127.0.0.1,0.0.0.0")
CSRF_TRUSTED_ORIGINS = env("DJANGO_CSRF_TRUSTED_ORIGINS", default="http://localhost:8000").split(",")
AUTH_GROUP_MODEL = 'identity.Group'

# === Applications === #
DEFAULT_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
]

THIRD_PARTY_APPS = [
    "rest_framework",
    'rest_framework.authtoken',
    "django_filters",
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',
    "corsheaders",
    "storages",
    'django_extensions',
    'drf_yasg',
    'modeltranslation',
    "django_guid",
    "django_quill",
    'import_export',
    'dj_rest_auth',
    'rest_framework_api_key',
    'django_group_model',
    'django_user_agents',
    'django_prometheus',
]

SELF_APPS = [
    'apps.core',
    'apps.identity',
    'apps.user',
    'apps.file_manager',
    'apps.notification',
    'apps.payment',
    'apps.product',
    'apps.cms',
    'apps.setting',
    'apps.report',
    'apps.integration',
    'apps.blog',
    'apps.crm',
    'apps.plan',
    'apps.billing',
    'apps.subscription',
    'apps.discount',
    'apps.kanban',
    'apps.easyaudit',
]

INSTALLED_APPS = DEFAULT_APPS + THIRD_PARTY_APPS + SELF_APPS + [
    'django.contrib.sites',
    'allauth',  # maybe not needed
    'allauth.account',  # maybe not needed
    'allauth.socialaccount',
    'allauth.socialaccount.providers.google',
    'allauth.socialaccount.providers.facebook',
    'social_django',
    'django_celery_beat',
    'channels',
    'easy_thumbnails',
    'encrypted_model_fields',
]

ENABLE_UNFOLD = env.bool("ENABLE_UNFOLD", default=True)
if ENABLE_UNFOLD:
    INSTALLED_APPS.insert(0, "unfold")

# === Middleware === #
MIDDLEWARE = [
    'django_prometheus.middleware.PrometheusBeforeMiddleware',
    "django.middleware.security.SecurityMiddleware",
    'django_guid.middleware.guid_middleware',
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    'django.middleware.locale.LocaleMiddleware',
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    'allauth.account.middleware.AccountMiddleware',
    'apps.easyaudit.middleware.easyaudit.EasyAuditMiddleware',
    'apps.easyaudit.middleware.custom_easyaudit.RequestEventMiddleware',
    'apps.easyaudit.middleware.custom_easyaudit.CustomEasyAuditLoginEventMiddleware',
    'django_user_agents.middleware.UserAgentMiddleware',
    "apps.identity.middlewares.ip_middleware.IPMiddleware",
    "django_prometheus.middleware.PrometheusAfterMiddleware",
    "apps.identity.middlewares.device_track_middleware.XForwardedForMiddleware",
    "apps.identity.middlewares.device_track_middleware.DeviceTrackMiddleware"
    # 'common.middleware.LicenseKeyMiddleware',
]

# === URL Configuration === #
ROOT_URLCONF = "config.urls"

# === Templates === #
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

# === WSGI & ASGI Applications === #
WSGI_APPLICATION = "config.wsgi.application"
ASGI_APPLICATION = 'config.asgi.application'

# === Password Validation === #
AUTH_PASSWORD_VALIDATORS = [
    {"NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator"},
    {"NAME": "django.contrib.auth.password_validation.MinimumLengthValidator"},
    {"NAME": "django.contrib.auth.password_validation.CommonPasswordValidator"},
    {"NAME": "django.contrib.auth.password_validation.NumericPasswordValidator"},
]

# === Internationalization === #
LANGUAGE_CODE = "en"
TIME_ZONE = "UTC"
USE_I18N = True
USE_TZ = True

# Available Timezones
TIMEZONE_CHOICES = [
    ('UTC', 'UTC'),
    ('US/Pacific', 'US/Pacific'),
    ('US/Eastern', 'US/Eastern'),
    ('Asia/Dhaka', 'Asia/Dhaka'),
    ('Asia/Dubai', 'Asia/Dubai'),
    ('Asia/Kolkata', 'Asia/Kolkata'),
    ('Asia/Tokyo', 'Asia/Tokyo'),
    ('Europe/London', 'Europe/London'),
    ('Europe/Paris', 'Europe/Paris'),
]

# === AWS Configuration === #
AWS_ACCESS_KEY_ID = env('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = env('AWS_SECRET_ACCESS_KEY')
AWS_DEFAULT_REGION = env('AWS_DEFAULT_REGION', default='us-east-1')

# === Static & Media Files === #
DJANGO_DEFAULT_FILE_STORAGE = env('DJANGO_DEFAULT_FILE_STORAGE', default='local')
if DJANGO_DEFAULT_FILE_STORAGE == 's3':
    STORAGES = {
        "default": {"BACKEND": "storages.backends.s3.S3Storage"},
        "staticfiles": {"BACKEND": "storages.backends.s3.S3Storage"},
    }
    AWS_STORAGE_BUCKET_NAME = env('AWS_STORAGE_BUCKET_NAME')
    AWS_S3_REGION_NAME = env('AWS_S3_REGION_NAME', default=AWS_DEFAULT_REGION)
    AWS_S3_CUSTOM_DOMAIN = env('AWS_S3_CUSTOM_DOMAIN', default='cdn.devxhub.com')
    STATIC_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/static/'
    MEDIA_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/media/'
    MEDIA_BASE_URL = ""
else:
    STORAGES = {
        "default": {"BACKEND": "django.core.files.storage.FileSystemStorage"},
        "staticfiles": {"BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage"},
    }

    STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
    STATICFILES_DIRS = [
        os.path.join(BASE_DIR, 'static'),
    ]
    STATIC_URL = "/static/"
    MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
    MEDIA_URL = '/media/'
    MEDIA_BASE_URL = env('MEDIA_BASE_URL', default='http://localhost:8000')


# === Authentication === #
AUTHENTICATION_BACKENDS = (
    'social_core.backends.google.GoogleOAuth2',
    'social_core.backends.facebook.FacebookOAuth2',
    'django.contrib.auth.backends.ModelBackend',
    'allauth.account.auth_backends.AuthenticationBackend',
)
AUTH_USER_MODEL = 'identity.User'
GOOGLE_REDIRECT_URL = env('GOOGLE_REDIRECT_URL', default='http://localhost:3000/en/oauth/google/callback')
FACEBOOK_REDIRECT_URL = env('FACEBOOK_REDIRECT_URL', default='http://localhost:3000/en/oauth/facebook/callback')

REST_AUTH = {
    'USE_JWT': True,
}


SITE_ID = 1
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_USERNAME_REQUIRED = False
ACCOUNT_LOGIN_ON_EMAIL_CONFIRMATION = True
ACCOUNT_AUTHENTICATION_METHOD = env('ACCOUNT_AUTHENTICATION_METHOD', default='email')
ACCOUNT_EMAIL_VERIFICATION = env('ACCOUNT_EMAIL_VERIFICATION', default='mandatory')

SOCIAL_AUTH_PIPELINE = (
    'social_core.pipeline.social_auth.social_details',
    'social_core.pipeline.social_auth.social_uid',
    'social_core.pipeline.social_auth.auth_allowed',
    'social_core.pipeline.social_auth.social_user',
    'social_core.pipeline.user.get_username',
    'social_core.pipeline.user.create_user',
    'social_core.pipeline.social_auth.associate_user',
    'social_core.pipeline.social_auth.load_extra_data',
    'social_core.pipeline.user.user_details',
)

# === Django REST Framework === #
#  anonimus user can make 5 requests per minute set veriables in .env file

ANONIMUS_API_RATE_LIMIT = env('ANONIMUS_API_RATE_LIMIT', default='100/hour')
AUTHENTICATED_API_RATE_LIMIT = env('AUTHENTICATED_API_RATE_LIMIT', default='1000/minute')


REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        # 'rest_framework.authentication.SessionAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ),
    'DEFAULT_RENDERER_CLASSES': (
        'dxh_common.renderers.CustomJSONRenderer',
        'rest_framework.renderers.JSONRenderer',
    ),
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
    ],
    'EXCEPTION_HANDLER': 'dxh_common.exceptions.custom_exception_handler',
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle',
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': ANONIMUS_API_RATE_LIMIT,
        'user': AUTHENTICATED_API_RATE_LIMIT,
        "login": "5/min",
    }
}

# === Simple JWT === #
SIMPLE_JWT = SIMPLE_JWT

# === Payment === #
PAYMENT_CURRENCY = 'USD'
PAYMENT_DECIMAL_PLACES = 2
MAX_PAYMENT_AMOUNT = 999999


# === Valkey & Celery === #
VALKEY_URL = env("VALKEY_URL", default="valkey://valkey:6379/0")
CELERY_BROKER_URL = env("CELERY_BROKER_URL", default="redis://valkey:6379/0")
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_valkey.core.ValkeyChannelLayer",
        "CONFIG": {
            "hosts": [VALKEY_URL],
        },
    },
}

# === Miscellaneous === #
UNFOLD = UNFOLD_CONFIG

# Attach the logging configuration
# LOGGING = LOGGING

# URL of the frontend application
FRONTEND_URL = env("FRONTEND_URL", default="http://localhost:3000")

CORS_ALLOW_CREDENTIALS = True
CORS_ORIGIN_ALLOW_ALL = False
CORS_ALLOWED_ORIGINS = env("DJANGO_TRUSTED_CORS_ORIGINS", default="http://localhost:3000,http://localhost:4200").split(",")
CORS_ALLOW_ALL_ORIGINS = env.bool("CORS_ALLOW_ALL_ORIGINS", default=False)
CORS_ALLOW_HEADERS = list(default_headers) + [
    'X-Captcha-Token',
    'Platform-Type',
]
# === Internationalization === #
LANGUAGES = [
    ('en', _('English')),
    ('es', _('Spanish')),
    ('fr', _('French')),
    ('tr', _('Turkish')),
    ('bn', _('Bengali')),
]


LOCALE_PATHS = [
    BASE_DIR / 'locale',
]

# DJANGO GUID
DJANGO_GUID = {
    'GUID_HEADER_NAME': 'X-Request-ID',
    'VALIDATE_GUID': True,
    'RETURN_HEADER': True,
    'EXPOSE_HEADER': True,
    'INTEGRATIONS': [],
    'IGNORE_URLS': [],
    'UUID_LENGTH': 32,
}

# === OpenAI === #
ENABLE_OPENAI = env.bool("ENABLE_OPENAI", default=False)
OPENAI_API_KEY = env('OPENAI_API_KEY', default='')
OPENAI_MAX_TOKENS = env("OPENAI_MAX_TOKENS", default=150)

# # === Cookie Settings === #
# SESSION_COOKIE_SECURE = True  # Use True in production (requires HTTPS)
# CSRF_COOKIE_SECURE = True     # Use True in production (requires HTTPS)
# SESSION_COOKIE_HTTPONLY = True  # Prevent JavaScript access
# CSRF_COOKIE_HTTPONLY = False   # False to allow JS access for CSRF token
# SESSION_COOKIE_SAMESITE = 'Lax'  # 'Lax' for better CSRF protection, 'None' for cross-origin
# CSRF_COOKIE_SAMESITE = 'Lax'    # Same as above

# # === Production Overrides === #
# if not DEBUG:
#     SESSION_COOKIE_SECURE = True
#     CSRF_COOKIE_SECURE = True
#     SESSION_COOKIE_SAMESITE = 'None'  # Cross-origin cookies for frontend
#     CSRF_COOKIE_SAMESITE = 'None'

# SECURE_BROWSER_XSS_FILTER = True
# SECURE_CONTENT_TYPE_NOSNIFF = True
# X_FRAME_OPTIONS = 'DENY'
# SECURE_HSTS_SECONDS = 3600
# SECURE_HSTS_INCLUDE_SUBDOMAINS = True
# SECURE_HSTS_PRELOAD = True
# SECURE_SSL_REDIRECT = True
# SESSION_COOKIE_SECURE = True
# CSRF_COOKIE_SECURE = True

# === Easy Thumbnails === #
THUMBNAIL_ALIASES = {
    '': {
        'icon': {'size': (16, 16), 'crop': True},
        'avatar': {'size': (32, 32), 'crop': True},
        'avatar_small': {'size': (64, 64), 'crop': True},
        'profile': {'size': (320, 320), 'crop': True},
    },
}


# license key
LICENSE_KEY = env("LICENSE_KEY", default="#42342")

# === Stripe === #
ENABLE_STRIPE = env.bool("ENABLE_STRIPE", default=False)
STRIPE_PUBLIC_KEY = env('STRIPE_PUBLIC_KEY', default='')
STRIPE_SECRET_KEY = env('STRIPE_SECRET_KEY', default='')
STRIPE_WEBHOOK_SECRET = env('STRIPE_WEBHOOK_SECRET', default='')

# === Swagger === #
ENABLE_SWAGGER = env.bool("ENABLE_SWAGGER", default=True)

# === Quill Editor === #
QUILL_CONFIGS = QUILL_CONFIG

# === MFA APP NAME === #
APP_NAME = env("APP_NAME", default="devxhub Black Office")

# === AWS Configuration === #
USE_AWS_S3 = env.bool("USE_AWS_S3", default=False)

# === DEEPSEEK  === #
DEEPSEEK_API_KEY = env("DEEPSEEK_API_KEY", default="")

# === Sentry === #

ENABLE_SENTRY = env.bool("ENABLE_SENTRY", default=False)
SENTRY_DSN = env("SENTRY_DSN", default="")

if not DEBUG and ENABLE_SENTRY:
    integrations = [
        DjangoIntegration(),
        CeleryIntegration(),
        RedisIntegration(),
    ]
    sentry_sdk.init(
        dsn=SENTRY_DSN,
        environment=env("SENTRY_ENV", default="production"),
        integrations=integrations,
        traces_sample_rate=0.5,
        send_default_pii=False,
    )

# Custom API Key header
API_KEY_CUSTOM_HEADER = "HTTP_X_API_KEY"
PLATFORM_TYPE = "PLATFORM_TYPE"

# Django Encryption Fields
FIELD_ENCRYPTION_KEY = env('FIELD_ENCRYPTION_KEY', default='tzhVN_tY_4Fr6KfUgoZNWaCTnh1_BEFZBC8HRD95Gxk=')


# === FCM Django === #

# Store an environment variable called GOOGLE_APPLICATION_CREDENTIALS
# which is a path that point to a json file with your credentials.
# Additional arguments are available: credentials, options, name

FIREBASE_APP = initialize_app()

# To learn more, visit the docs here:
# https://cloud.google.com/docs/authentication/getting-started>

FCM_DJANGO_SETTINGS = FCM_DJANGO_SETTINGS


# GeoIP2 Configuration
GEOIP_PATH = 'geoip'
GEOIP_COUNTRY = 'GeoLite2-Country.mmdb'
GEOIP_CITY = 'GeoLite2-City.mmdb'

# === Easy Audit === #
DJANGO_EASY_AUDIT_READONLY_EVENTS = True

