from typing import List
from django.contrib import admin
from django.contrib.sites.models import Site
from django.contrib.sites.admin import SiteAdmin
from django_celery_beat.admin import (
    ClockedScheduleAdmin as BaseClockedScheduleAdmin,
    CrontabScheduleAdmin as BaseCrontabScheduleAdmin,
)
from django_celery_beat.models import (
    ClockedSchedule,
    CrontabSchedule,
    IntervalSchedule,
    PeriodicTask,
    SolarSchedule,
)
from social_django.models import Association, Nonce, UserSocialAuth
from social_django.admin import (
    UserSocialAuthOption,
    NonceOption,
    AssociationOption,
)

from apps.easyaudit.admin import CRUDEventAdmin, LoginEventAdmin, RequestEventAdmin
from apps.easyaudit.models import CRUDEvent, LoginEvent, RequestEvent

from rest_framework_simplejwt.token_blacklist.models import (
    OutstandingToken,
    BlacklistedToken,
)
from rest_framework.authtoken.models import TokenProxy
from rest_framework.authtoken.admin import TokenAdmin
from rest_framework_simplejwt.token_blacklist.admin import (
    OutstandingTokenAdmin,
    BlacklistedTokenAdmin,
)

from dxh_common.admin import BaseAdmin,SocialAppForm
from allauth.account.models import EmailAddress
from allauth.account.admin import EmailAddressAdmin as AllauthEmailAddressAdmin
from allauth.socialaccount.models import SocialApp, SocialToken, SocialAccount
from allauth.socialaccount.admin import (
    SocialAppAdmin as AllauthSocialAppAdmin,
    SocialTokenAdmin as AllauthSocialTokenAdmin,
    SocialAccountAdmin as AllauthSocialAccountAdmin,
)
from rest_framework_api_key.models import APIKey
from rest_framework_api_key.admin import APIKeyModelAdmin


# Authtoken/tokenproxy
# --------------------------------------------------------
# UNREGISTER DEFAULT MODELS
# --------------------------------------------------------

# Celery Beat
admin.site.unregister(PeriodicTask)
admin.site.unregister(IntervalSchedule)
admin.site.unregister(CrontabSchedule)
admin.site.unregister(SolarSchedule)
admin.site.unregister(ClockedSchedule)

# Allauth
admin.site.unregister(EmailAddress)
admin.site.unregister(SocialApp)
admin.site.unregister(SocialToken)
admin.site.unregister(SocialAccount)

# EasyAudit
admin.site.unregister(CRUDEvent)
admin.site.unregister(LoginEvent)
admin.site.unregister(RequestEvent)

# Django REST Framework Simple JWT
admin.site.unregister(OutstandingToken)
admin.site.unregister(BlacklistedToken)
admin.site.unregister(TokenProxy)

# Social Django
admin.site.unregister(UserSocialAuth)
admin.site.unregister(Association)
admin.site.unregister(Nonce)

# Django Sites Framework
admin.site.unregister(Site)

# Django Rest Framework API Key
admin.site.unregister(APIKey)

# --------------------------------------------------------
# CUSTOM ADMIN REGISTRATIONS
# --------------------------------------------------------

# Celery Beat
@admin.register(IntervalSchedule)
class IntervalScheduleAdmin(BaseAdmin):
    pass


@admin.register(CrontabSchedule)
class CrontabScheduleAdmin(BaseCrontabScheduleAdmin, BaseAdmin):
    pass


@admin.register(SolarSchedule)
class SolarScheduleAdmin(BaseAdmin):
    pass


@admin.register(ClockedSchedule)
class ClockedScheduleAdmin(BaseClockedScheduleAdmin, BaseAdmin):
    pass


# Allauth
@admin.register(EmailAddress)
class CustomEmailAddressAdmin(AllauthEmailAddressAdmin, BaseAdmin):
    pass


  
@admin.register(SocialApp)
class CustomSocialAppAdmin(AllauthSocialAppAdmin, BaseAdmin):
    form = SocialAppForm
    


@admin.register(SocialToken)
class CustomSocialTokenAdmin(AllauthSocialTokenAdmin, BaseAdmin):
    pass


@admin.register(SocialAccount)
class CustomSocialAccountAdmin(AllauthSocialAccountAdmin, BaseAdmin):
    pass


# EasyAudit
@admin.register(CRUDEvent)
class CustomCRUDEventAdmin(CRUDEventAdmin, BaseAdmin):
    pass


@admin.register(LoginEvent)
class CustomLoginEventAdmin(LoginEventAdmin, BaseAdmin):
    pass


@admin.register(RequestEvent)
class CustomRequestEventAdmin(RequestEventAdmin, BaseAdmin):
    pass


# Django REST Framework Simple JWT
@admin.register(OutstandingToken)
class CustomOutstandingTokenAdmin(OutstandingTokenAdmin, BaseAdmin):
    pass


@admin.register(BlacklistedToken)
class CustomBlacklistedTokenAdmin(BlacklistedTokenAdmin, BaseAdmin):
    pass

@admin.register(TokenProxy)
class DefaultTokenModelAdmin(TokenAdmin, BaseAdmin):
    pass


# Social Django
@admin.register(UserSocialAuth)
class CustomUserSocialAuthOption(UserSocialAuthOption, BaseAdmin):
    pass


@admin.register(Nonce)
class CustomNonceOption(NonceOption, BaseAdmin):
    pass


@admin.register(Association)
class CustomAssociationAdmin(AssociationOption, BaseAdmin):
    pass


# Django Sites Framework
@admin.register(Site)
class CustomSiteAdmin(SiteAdmin, BaseAdmin):
    pass


# Django Rest Framework API key
@admin.register(APIKey)
class CustomAPIKeyAdmin(APIKeyModelAdmin, BaseAdmin):
    pass