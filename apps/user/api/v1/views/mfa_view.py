import pyotp
from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView, PublicApiView
from dxh_common.utils.enums import MFAType

from apps.user.services import MFAService, UserPreferenceService
from apps.identity.services import AuthService, UserService
from apps.identity.api.v1.serializers import UserSerializer

logger = Logger(__name__)


class SetupMFAView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.mfa_service = MFAService()
        self.user_preference = UserPreferenceService()

    def post(self, request):
        try:
            user = request.user

            mfa_setup = self.mfa_service.update_or_create_mfa_setup(user)
            secret = mfa_setup.secret
            backup_codes = mfa_setup.backup_codes

            self.user_preference.update_user_preference(user, {"mfa_enabled": True})

            totp = pyotp.TOTP(secret)
            provisioning_uri = totp.provisioning_uri(
                request.user.email, issuer_name="GPT360"
            )

            result = {
                "message": _("MFA setup successful"),
                "data": {
                    "secret": secret,
                    "backup_codes": backup_codes,
                    "qr_uri": provisioning_uri,
                }
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "SetupMFAView:post",
                         "message": "Unexpected error occurred", "error": str(e)})
            raise e


class VerifyMFAView(PublicApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.mfa_service = MFAService()
        self.auth_service = AuthService()
        self.user_service = UserService()

    def post(self, request):
        code = request.data.get("code")
        mfa_token = request.data.get("mfa_token")

        if not code:
            result = {
                "message": _("MFA code is required"),
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        try:
            if not mfa_token:
                result = {
                    "message": _("MFA token is required"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            session_data = self.auth_service._get_mfa_session(mfa_token)
            if not session_data:
                result = {
                    "message": _("Invalid or expired MFA session"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            user = self.user_service.get(id=session_data['user_id'])
            if not user:
                result = {
                    "message": _("User not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            mfa_setup = self.mfa_service.get_mfa_setup(
                user=user, type=MFAType.TOTP.value
            )
            totp = pyotp.TOTP(mfa_setup.secret)

            if not (totp.verify(code) or self.mfa_service.verify_backup_code(user, code)):
                if 'token_obj' in session_data:
                    self.auth_service.verification_token_service.increment_attempts(session_data['token_obj'])

                result = {
                    "message": _("Invalid MFA code"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            user, tokens, message = self.auth_service.complete_mfa_login(mfa_token)

            serializer = UserSerializer(user)
            result = {
                "message": message,
                "data": {
                    "tokens": tokens,
                    "user": serializer.data,
                },
            }
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "VerifyMFAView:post",
                        "message": "Unexpected error occurred", "error": str(e)})
            raise e


class DisableMFAView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.mfa_service = MFAService()
        self.user_preference = UserPreferenceService()

    def post(self, request):
        try:
            user = request.user
            mfa_setup = self.mfa_service.get_mfa_setup(user=user)
            if not mfa_setup:
                result = {
                    "message": _("MFA is not enabled for this user"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            mfa_setup.delete()

            self.user_preference.update_user_preference(user, {"mfa_enabled": False})

            result = {
                "message": _("MFA disabled successfully"),
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "DisableMFAView:post",
                         "message": "Unexpected error occurred", "error": str(e)})
            raise e
