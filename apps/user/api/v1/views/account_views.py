import json
from django.http import HttpResponse
from django.utils import timezone
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import Response, status
from dxh_libraries.translation import gettext_lazy as _

from apps.identity.services import UserService
from apps.core.services import SystemSettingService
from apps.user.services import AccountDeletionService, UserDataExportService

logger = Logger(__name__)


class RequestDeletionView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()
        self.system_settings_service = SystemSettingService()
        self.account_deletion_service = AccountDeletionService()

    def post(self, request):
        try:
            user = self.user_service.get(id=request.user.id)
            if user != request.user:
                result = {
                    "message": _("Permission denied")
                }
                return Response(result, status=status.HTTP_403_FORBIDDEN)

            system_settings = self.system_settings_service.get(company=user.company)
            if not system_settings:
                result = {
                    "message": _("System settings not configured")
                }
                return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            days_until_deletion = system_settings.days_until_account_deletion

            deletion_result = self.account_deletion_service.request_account_deletion(
                user=user,
                days_until_deletion=days_until_deletion
            )

            result = {
                "message": deletion_result['message'],
                "data": {
                    "task_id": deletion_result['task_id'],
                    "scheduled_in_days": deletion_result['scheduled_in_days'],
                    "scheduled_at": deletion_result['scheduled_at'].isoformat(),
                }
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "RequestDeletionView:post",
                        "message": "Unexpected error occurred", "error": str(e)})
            raise e


class ExportDataView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()
        self.user_data_export_service = UserDataExportService()

    def get(self, request):
        try:
            user = self.user_service.get(id=request.user.id)
            if user != request.user:
                result = {
                    "message": _("Permission denied")
                }
                return Response(result, status=status.HTTP_403_FORBIDDEN)

            export_result = self.user_data_export_service.export_user_data(user)
            json_content = json.dumps(export_result, indent=4, ensure_ascii=False)

            response = HttpResponse(json_content, content_type='application/json')
            response['Content-Disposition'] = f'attachment; filename="user_data_{user.id}_{timezone.now().date()}.json"'
            
            return response

        except Exception as e:
            logger.error({"event": "ExportDataView:get",
                        "message": "Unexpected error occurred", "error": str(e)})
            raise e

