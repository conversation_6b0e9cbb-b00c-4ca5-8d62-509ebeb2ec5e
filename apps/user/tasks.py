from asgiref.sync import async_to_sync
from dxh_libraries.celery import shared_task
from dxh_libraries.channels import get_channel_layer
from dxh_common.logger import Logger

from apps.identity.services import UserService

logger = Logger(__name__)


@shared_task
def delete_user_after_delay(user_id):
    user_service = UserService()
    try:
        user = user_service.get(id=user_id)
        if user:
            user_service.delete(user)
            logger.info(f"User {user_id} has been deleted.")
        else:
            logger.info(f"User {user_id} not found for deletion.")
    except Exception as e:
        logger.error(f"Error deleting user {user_id}: {str(e)}")