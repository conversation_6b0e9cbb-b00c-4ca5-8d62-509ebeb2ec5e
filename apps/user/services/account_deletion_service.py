from celery import current_app
from django.utils import timezone
from django.core.cache import cache
from dxh_common.logger import Logger
from dxh_common.base.base_service import ServiceError

from apps.user.tasks import delete_user_after_delay

logger = Logger(__name__)


class AccountDeletionService:
    def __init__(self):
        pass

    def request_account_deletion(self, user, days_until_deletion):
        try:
            self.cancel_deletion_request(user)

            countdown = days_until_deletion * 24 * 60 * 60

            task_result = delete_user_after_delay.apply_async(
                args=[user.id],
                countdown=countdown
            )

            cache_key = f"user_deletion_task_{user.id}"
            cache_data = {
                'task_id': task_result.id,
                'scheduled_at': timezone.now() + timezone.timedelta(days=days_until_deletion),
                'user_id': user.id
            }
            cache_timeout = (days_until_deletion + 1) * 24 * 60 * 60
            cache.set(cache_key, cache_data, timeout=cache_timeout)

            data = {
                'task_id': task_result.id,
                'scheduled_in_days': days_until_deletion,
                'scheduled_at': timezone.now() + timezone.timedelta(days=days_until_deletion),
                'message': f'Account will be deleted in {days_until_deletion} days. Log in to cancel.'
            }

            return data

        except Exception as e:
            logger.error(f"Error requesting account deletion for user {user.id}: {str(e)}")
            raise ServiceError(f"Failed to request account deletion: {str(e)}")

    def cancel_deletion_request(self, user, reason='manual_cancellation'):
        try:
            revoked_count = 0

            cache_key = f"user_deletion_task_{user.id}"
            cached_task = cache.get(cache_key)

            if cached_task and cached_task.get('task_id'):
                task_id = cached_task['task_id']
                try:
                    current_app.control.revoke(task_id, terminate=True)
                    revoked_count += 1
                    logger.info(f"Revoked deletion task {task_id} for user {user.id}")

                    cache.delete(cache_key)
                except Exception as revoke_error:
                    logger.warning(f"Failed to revoke cached task {task_id}: {str(revoke_error)}")

            inspect = current_app.control.inspect()
            active_tasks = inspect.active() or {}
            scheduled_tasks = inspect.scheduled() or {}
            reserved_tasks = inspect.reserved() or {}

            all_tasks = []
            for _, tasks in active_tasks.items():
                all_tasks.extend(tasks)
            for _, tasks in scheduled_tasks.items():
                all_tasks.extend(tasks)
            for _, tasks in reserved_tasks.items():
                all_tasks.extend(tasks)

            for task_info in all_tasks:
                task_id = task_info.get('id') or task_info.get('uuid')

                if (task_id and
                    task_info.get('name') == 'apps.user.tasks.delete_user_after_delay' and
                    task_info.get('args') and len(task_info['args']) > 0 and
                    task_info['args'][0] == user.id):

                    current_app.control.revoke(task_id, terminate=True)
                    revoked_count += 1
                    logger.info(f"Revoked active deletion task {task_id} for user {user.id}")

            if revoked_count > 0:
                logger.info(f"Cancelled {revoked_count} deletion task(s) for user {user.id}, reason: {reason}")
                return True
            else:
                return False

        except Exception as e:
            logger.error(f"Error cancelling deletion request for user {user.id}: {str(e)}")
            return False

    def has_pending_deletion_request(self, user):
        try:
            cache_key = f"user_deletion_task_{user.id}"
            cached_task = cache.get(cache_key)

            if cached_task and cached_task.get('task_id'):
                return True, cached_task

            return False, None

        except Exception as e:
            logger.error(f"Error checking pending deletion request for user {user.id}: {str(e)}")
            return False, None
