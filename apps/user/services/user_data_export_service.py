from django.utils import timezone
from dxh_common.logger import Logger
from dxh_common.base.base_service import ServiceError

from apps.notification.services import InAppNotificationService, EmailNotificationService
from apps.user.services import UserPreferenceService
f

logger = Logger(__name__)


class UserDataExportService:
    def __init__(self):
        self.in_app_notification_service = InAppNotificationService()
        self.email_notification_service = EmailNotificationService()
        self.user_preference_service = UserPreferenceService()

    def export_user_data(self, user):
        try:
            user_data = {
                'profile': self._get_user_profile(user),
                'notifications': self._get_notifications(user),
                'preferences': self._get_user_preferences(user),
            }

            user_data = {
                "timestamp": timezone.now().isoformat(),
                "status": "success",
                "message": "Data export successful",
                "data": user_data,
                "user_id": user.id,
                "user_email": user.email
            }

            return user_data

        except Exception as e:
            logger.error(f"Error exporting user data for user {user.id}: {str(e)}")
            raise ServiceError(f"Failed to export user data: {str(e)}")

    def _get_user_profile(self, user):
        try:
            profile_data = {
                'id': user.id,
                'email': user.email,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'phone': user.phone,
                'is_active': user.is_active,
                'is_email_verified': user.is_email_verified,
                'is_phone_verified': getattr(user, 'is_phone_verified', False),
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'date_joined': user.date_joined.isoformat() if user.date_joined else None,
            }

            user_profile = getattr(user, 'user_profiles', None)
            if user_profile:
                profile_data['profile'] = {
                    'date_of_birth': user_profile.date_of_birth.isoformat() if user_profile.date_of_birth else None,
                    'bio': user_profile.bio,
                    'profile_picture': user_profile.profile_picture.url if user_profile.profile_picture else None,
                }

            return profile_data
        
        except Exception as e:
            logger.warning(f"Error getting user profile: {str(e)}")
            return {}

    def _get_notifications(self, user):
        """Get user notifications"""
        try:
            in_app_notifications = self.in_app_notification_service.list(user=user).order_by('-created_at')[:100]

            email_notifications = self.email_notification_service.list(user=user).order_by('-created_at')[:100]

            notification_data = {
                'in_app_notifications': [
                    {
                        'id': notif.id,
                        'title': notif.title,
                        'message': notif.message,
                        'is_read': notif.is_read,
                        'created_at': notif.created_at.isoformat() if notif.created_at else None,
                    }
                    for notif in in_app_notifications
                ],
                'email_notifications': [
                    {
                        'id': notif.id,
                        'recipient_email': notif.recipient_email,
                        'subject': notif.subject,
                        'body': notif.body,
                        'created_at': notif.created_at.isoformat() if notif.created_at else None,
                    }
                    for notif in email_notifications
                ]
            }

            return notification_data
        
        except Exception as e:
            logger.warning(f"Error getting notifications: {str(e)}")
            return {'in_app_notifications': [], 'email_notifications': []}


    def _get_user_preferences(self, user):
        """Get user preferences and settings"""
        try:
            user_pref = getattr(user, 'userpreference', None)
            preferences = {}
            if user_pref:
                preferences = {
                    'language': user_pref.language,
                    'timezone': user_pref.timezone.name if user_pref.timezone else None,
                    'dark_mode': user_pref.dark_mode,
                    'mfa_enabled': user_pref.mfa_enabled,
                    'two_factor_enabled': user_pref.two_factor_enabled,
                    'email_notifications': user_pref.email,
                    'sms_notifications': user_pref.sms,
                    'push_notifications': user_pref.push_notification,
                    'notification_sound': user_pref.notification_sound,
                }

            # Notification preferences
            notif_pref = self.user_preference_service.get(user=user)
            notification_preferences = {
                'email_enabled': notif_pref.email_enabled,
                'sms_enabled': notif_pref.sms_enabled,
                'push_enabled': notif_pref.push_enabled,
                'in_app_enabled': notif_pref.in_app_enabled,
                'silent_hours_start': notif_pref.silent_hours_start.isoformat() if notif_pref.silent_hours_start else None,
                'silent_hours_end': notif_pref.silent_hours_end.isoformat() if notif_pref.silent_hours_end else None,
            }

            preferences = {
                'user_preferences': preferences,
                'notification_preferences': notification_preferences
            }

            return preferences
        
        except Exception as e:
            logger.warning(f"Error getting user preferences: {str(e)}")
            return {'user_preferences': {}, 'notification_preferences': {}}
